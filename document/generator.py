#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import uuid
import re
import json
import time
import logging
import requests
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml.ns import qn
from docx.opc.constants import RELATIONSHIP_TYPE
from .party_template_processor import PartyTemplateProcessor

# 尝试导入复选框处理模块
try:
    from document.checkbox import CheckboxProcessor
except ImportError:
    # 如果找不到模块，创建一个空的Checkbox处理器类
    class CheckboxProcessor:
        def __init__(self, *args, **kwargs):
            pass

        def process_checkboxes(self, *args, **kwargs):
            print("警告: 复选框处理模块未找到，跳过复选框处理")
            return False

# 尝试导入Flask相关模块，如果不在Flask环境中则忽略
try:
    from flask import current_app
except ImportError:
    current_app = None

# 配置日志记录器
logger = logging.getLogger(__name__)

class DocumentGenerator:
    """Word文档生成器"""

    def __init__(self, template_path=None, llm_api_url=None, llm_api_key=None, config=None):
        """
        初始化文档生成器

        Args:
            template_path (str, optional): 模板文件路径
            llm_api_url (str, optional): LLM API的URL
            llm_api_key (str, optional): LLM API的密钥
            config (dict, optional): 配置信息
        """
        self.template_path = template_path

        # 确保模板路径存在
        if template_path and not os.path.exists(template_path):
            print(f"警告: 指定的模板路径不存在: {template_path}")
            raise FileNotFoundError(f"模板文件不存在: {template_path}")

        self.placeholders = {}
        self.config = config or {}
        self.doc = Document(template_path)

        # 分析模板中的占位符
        print(f"分析模板: {os.path.basename(template_path) if template_path else '无'}")
        print(f"模板完整路径: {template_path}")

        # 检查模板中是否有占位符
        has_placeholders = False
        for paragraph in self.doc.paragraphs:
            if re.search(r'\{\{.*?\}\}', paragraph.text):
                has_placeholders = True
                break

        if not has_placeholders:
            print(f"警告: 模板 {os.path.basename(template_path)} 中未找到占位符")

        self._checkbox_llm_info_printed = False  # 添加标记，用于控制LLM处理复选框的信息只输出一次

        # 从环境变量或配置中获取复选框处理设置
        if 'process_checkboxes' not in self.config:
            # 默认完全不处理复选框，除非环境变量中指定
            env_process_checkboxes = os.environ.get('PROCESS_CHECKBOXES', '').lower()
            self.config['process_checkboxes'] = env_process_checkboxes in ('true', 'yes', '1', 'on')

        if 'use_llm_for_checkbox' not in self.config:
            # 默认不使用LLM处理复选框，除非环境变量中指定
            env_use_llm = os.environ.get('USE_LLM_FOR_CHECKBOX', '').lower()
            self.config['use_llm_for_checkbox'] = env_use_llm in ('true', 'yes', '1', 'on')

        if 'combine_checkboxes' not in self.config:
            # 默认不使用合并处理，除非环境变量中指定
            env_combine = os.environ.get('COMBINE_CHECKBOXES', '').lower()
            self.config['combine_checkboxes'] = env_combine in ('true', 'yes', '1', 'on')

        if 'combine_checkboxes_by' not in self.config:
            # 默认使用全文合并，除非环境变量中指定
            env_combine_by = os.environ.get('COMBINE_CHECKBOXES_BY', 'fulltext').lower()
            self.config['combine_checkboxes_by'] = env_combine_by if env_combine_by in ('fulltext', 'table') else 'fulltext'

        if 'process_checkbox_batch' not in self.config:
            # 默认不使用批量并发处理，除非环境变量中指定
            env_batch = os.environ.get('PROCESS_CHECKBOX_BATCH', '').lower()
            self.config['process_checkbox_batch'] = env_batch in ('true', 'yes', '1', 'on')

        # 初始化LLM相关参数
        self.llm_api_url = llm_api_url
        self.llm_api_key = llm_api_key

        # 如果在Flask环境中，从配置中获取LLM参数
        if current_app and not self.llm_api_url:
            self.llm_api_url = current_app.config.get('LLM_API_URL')
            self.llm_api_key = current_app.config.get('LLM_API_KEY')
            self.llm_model = current_app.config.get('LLM_MODEL')
            self.llm_temperature = current_app.config.get('LLM_TEMPERATURE', 0.3)
            self.llm_max_tokens = current_app.config.get('LLM_MAX_TOKENS', 1000)
            self.llm_top_p = current_app.config.get('LLM_TOP_P', 1.0)
        else:
            # 从配置中获取，如果配置中没有，则使用默认值
            self.llm_model = self.config.get('llm_model') if self.config.get('llm_model') else os.environ.get('LLM_MODEL', 'gpt-3.5-turbo')
            self.llm_temperature = self.config.get('llm_temperature', 0.3)
            self.llm_max_tokens = self.config.get('llm_max_tokens', 1000)
            self.llm_top_p = self.config.get('llm_top_p', 1.0)

        # 不再初始化共享的LLM处理器，让复选框处理器使用自己的配置
        self.shared_llm_processor = None
        print("DocumentGenerator: 不使用共享LLM处理器，复选框处理器将使用专用配置")

        # 初始化案件类型到复选框关键词的映射，与templates目录中的11个模板类型一致
        self.case_type_keywords = {
            # 1. 民间借贷纠纷相关关键词
            '民间借贷纠纷起诉状': [
                '借款合同', '借条', '欠条', '借据', '利息', '本金',
                '逾期', '担保', '还款方式', '民间借贷', '贷款', '还款',
                '借贷关系', '保证金', '违约金', '罚息', '民间借贷合同'
            ],

            # 2. 离婚纠纷相关关键词
            '离婚纠纷起诉状': [
                '子女抚养', '财产分割', '家庭暴力', '损害赔偿',
                '经济补偿', '经济帮助', '探视权', '抚养费用', '债务分担',
                '婚姻关系', '家暴', '子女护理', '共同财产', '婚前财产',
                '离婚协议', '结婚证', '共同债务'
            ],

            # 3. 买卖合同纠纷相关关键词
            '买卖合同纠纷起诉状': [
                '买卖合同', '购销合同', '商品', '货物', '交付', '验收',
                '质量', '价款', '货款', '违约金', '瑕疵', '退货',
                '销售', '购买', '买方', '卖方', '合同履行'
            ],

            # 4. 金融借款合同纠纷相关关键词
            '金融借款合同纠纷起诉状': [
                '金融借款', '银行贷款', '金融机构', '贷款合同', '贷款本金',
                '贷款利息', '银行', '金融', '抵押', '质押', '保证',
                '逾期利息', '罚息', '提前还款', '贷款期限'
            ],

            # 5. 物业服务合同纠纷相关关键词
            '物业服务合同纠纷起诉状': [
                '物业服务', '物业费', '物业管理', '小区', '业主', '物业公司',
                '物业合同', '服务质量', '共有部分', '公共设施', '维修基金',
                '业主委员会', '物业管理区域', '物业服务标准'
            ],

            # 6. 银行信用卡纠纷相关关键词
            '银行信用卡纠纷起诉状': [
                '信用卡', '银行卡', '透支', '刷卡', '还款', '信用额度',
                '信用卡账单', '分期付款', '手续费', '年费', '滞纳金',
                '信用卡协议', '银行', '持卡人', '信用卡消费'
            ],

            # 7. 机动车交通事故责任纠纷相关关键词
            '机动车交通事故责任纠纷起诉状': [
                '交通事故', '机动车', '车祸', '人身损害', '财产损失', '医疗费用',
                '误工费', '护理费', '营养费', '交通费', '住院费', '交通责任',
                '车辆保险', '赔偿标准', '精神损害', '后续治疗费', '肇事'
            ],

            # 8. 劳动争议相关关键词
            '劳动争议起诉状': [
                '劳动合同', '工资', '劳动报酬', '劳动保护', '劳动条件',
                '劳动安全', '社会保险', '医疗保险', '工伤', '赏金',
                '劳动争议调解', '劳动仲裁', '经济补偿金', '违法解除',
                '加班费', '年休假', '试用期', '劳动者', '用人单位'
            ],

            # 9. 融资租赁合同纠纷相关关键词
            '融资租赁合同纠纷起诉状': [
                '融资租赁', '租赁物', '租金', '租期', '融资', '出租人',
                '承租人', '租赁合同', '租赁期限', '租赁物所有权', '留购价款',
                '保证金', '租赁物交付', '租赁物维修', '租赁物灭失'
            ],

            # 10. 保证保险合同纠纷相关关键词
            '保证保险合同纠纷起诉状': [
                '保险', '保证', '保单', '理赔', '投保', '保费', '保险金',
                '保险责任', '保险事故', '保险期间', '保险合同', '被保险人',
                '保险人', '投保人', '受益人', '免责条款', '保险条款'
            ],

            # 11. 证券虚假陈述责任纠纷相关关键词
            '证券虚假陈述责任纠纷起诉状': [
                '证券', '股票', '虚假陈述', '投资', '股东', '上市公司',
                '信息披露', '虚假记载', '误导性陈述', '重大遗漏', '投资损失',
                '证券市场', '证券交易', '证券监管', '投资者', '董事会'
            ],

            # 物权纠纷相关关键词
            '物权纠纷': [
                '物权', '所有权', '用益物权', '地役权', '抵押权',
                '质押权', '留置权', '占有', '分割', '共有', '共同所有',
                '不动产登记', '土地使用权', '建筑物所有权'
            ]
        }

        # 混合表格中的列标识
        self.column_identifiers = {
            '借贷纠纷': ['借贷纠纷选项', '借贷纠纷列', '借贷相关'],
            '离婚纠纷': ['离婚纠纷选项', '离婚纠纷列', '离婚相关'],
            '劳动争议': ['劳动争议选项', '劳动争议列', '劳动相关'],
            '交通事故': ['交通事故选项', '交通事故列', '交通相关'],
            '物权纠纷': ['物权纠纷选项', '物权纠纷列', '物权相关']
        }

        if template_path:
            self.update_placeholders()

    def generate_document(self, case_data, output_path=None):
        """
        根据案件数据生成文档

        Args:
            case_data (dict): 案件数据
            output_path (str, optional): 输出文件路径或目录路径。
                                        如果是目录路径，将在该目录下生成文件。
                                        如果为None，则使用临时文件。

        Returns:
            str: 生成的文档路径
        """
        try:
            # 打印输入参数用于调试
            print(f"输入数据类型: {type(case_data).__name__}")
            if isinstance(case_data, dict):
                print(f"数据中的模板类型: {case_data.get('template_type', '无')}")

            # 检查模板是否存在
            if not self.template_path or not os.path.exists(self.template_path):
                logger.error(f"错误：模板文件不存在: {self.template_path}")
                return None

            # 输出最终使用的模板路径
            print(f"使用模板路径: {self.template_path}")

            # 生成文件名
            template_basename = os.path.basename(self.template_path)
            print(f"模板文件名: {template_basename}")

            # 生成带时间戳的输出文件名
            timestamp = int(time.time())
            output_filename = f"生成_{timestamp}_{template_basename}"

            # 处理输出路径逻辑
            if output_path is None:
                # 如果没有提供输出路径，创建临时目录
                import tempfile
                output_dir = tempfile.mkdtemp()
                final_output_path = os.path.join(output_dir, output_filename)
                print(f"创建临时目录作为输出: {output_dir}")
            elif os.path.isdir(output_path):
                # 如果提供的是目录路径，在该目录下生成文件
                output_dir = output_path
                final_output_path = os.path.join(output_dir, output_filename)
                print(f"将在目录 {output_dir} 下生成文件: {output_filename}")
            else:
                # 如果提供的是文件路径，直接使用该路径
                final_output_path = output_path
                output_dir = os.path.dirname(output_path)
                print(f"将使用指定的文件路径: {final_output_path}")

            try:
                # 加载模板文档
                doc = Document(self.template_path)
                print(f"已加载文档模板: {self.template_path}")

                # 处理文档（替换占位符并处理复选框）
                self._process_document(doc, case_data)

                # 确保输出目录存在
                os.makedirs(output_dir, exist_ok=True)

                # 保存文档
                doc.save(final_output_path)

                print(f"文档已生成: {final_output_path}")
                return final_output_path
            except Exception as e:
                logger.error(f"生成文档时出错: {str(e)}")
                raise

        except Exception as e:
            logger.error(f"生成文档时出错: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            logger.error(f"最后使用的模板路径: {self.template_path if hasattr(self, 'template_path') else '无模板路径'}")
            return None

    def _process_document(self, doc, data):
        """
        处理Word文档，替换占位符后分开处理复选框

        Args:
            doc (Document): Word文档对象
            data (dict): 数据字典
        """
        import logging
        logger = logging.getLogger('document.processor')

        # 1. 处理多当事人模板
        # 检查是否跳过当事人处理（优先检查专门的配置项）
        skip_party_processing = self.config.get('skip_party_processing')
        if skip_party_processing is None:
            # 如果没有专门配置，则使用skip_checkbox_processing作为后备
            skip_party_processing = self.config.get('skip_checkbox_processing', False)

        if skip_party_processing:
            print("📍 [文档生成] 跳过当事人处理（使用中间文档或配置要求）")
        else:
            # 通用模板始终在根模板目录中
            from config import Config
            templates_root_dir = Config.TEMPLATES_DIR
            party_processor = PartyTemplateProcessor(templates_dir=templates_root_dir, debug=True)
            print("📍 [文档生成] 处理多当事人模板，替换插入点标记")
            party_processor.process_party_templates(doc, data)

        # 2. 处理普通占位符
        print("处理文档中的普通占位符")
        self._replace_placeholders(doc, data)

        # 3. 处理复选框（如果配置）
        skip_checkbox = self.config.get('skip_checkbox_processing', False)
        process_checkbox = self.config.get('process_checkboxes', False)

        if skip_checkbox:
            print("📍 [文档生成] 来自编辑页面，使用简单字符串替换处理CHECKBOX")
            self._process_checkboxes_simple_replacement(doc, data)
        elif process_checkbox:
            print("处理文档中的复选框")
            self._process_checkboxes(doc, data)
        else:
            print("没有配置为处理复选框，跳过复选框处理")

        return doc

    def _process_checkboxes_simple_replacement(self, doc, data):
        """
        简单的CHECKBOX字符串替换处理（用于编辑页面生成）

        Args:
            doc: Word文档对象
            data (dict): 包含checkbox_N字段的数据
        """
        print("📍 [简单CHECKBOX替换] 开始处理CHECKBOX字符串替换")

        # 先查看所有数据键名
        all_keys = list(data.keys())
        checkbox_related_keys = [k for k in all_keys if 'checkbox' in k.lower()]
        print(f"📍 [简单CHECKBOX替换] 所有数据键数量: {len(all_keys)}")
        print(f"📍 [简单CHECKBOX替换] 包含'checkbox'的键: {checkbox_related_keys}")

        # 收集所有checkbox数据 - 支持多种格式
        checkbox_data = {}
        checkbox_mapping = {}  # 映射 checkbox_N -> 实际的表单字段值

        # 方式1: 直接匹配 checkbox_N 格式
        for key, value in data.items():
            if key.startswith('checkbox_') and key.replace('checkbox_', '').isdigit():
                checkbox_data[key] = bool(value)

        # 方式2: 匹配 checkbox_hash_N 格式并映射到 checkbox_N
        if not checkbox_data:
            import re
            checkbox_pattern = r'^checkbox_[a-f0-9]+_(\d+)$'  # checkbox_hash_number

            for key, value in data.items():
                match = re.match(checkbox_pattern, key)
                if match:
                    number = match.group(1)
                    standard_key = f"checkbox_{number}"
                    checkbox_data[standard_key] = bool(value)
                    checkbox_mapping[standard_key] = key
                    print(f"📍 [简单CHECKBOX替换] 映射 {key} -> {standard_key}")

        # 方式3: 如果还是没有，尝试所有包含checkbox的字段
        if not checkbox_data:
            for key, value in data.items():
                if 'checkbox' in key.lower():
                    # 尝试提取数字
                    import re
                    numbers = re.findall(r'\d+', key)
                    if numbers:
                        # 使用最后一个数字作为序号
                        number = numbers[-1]
                        standard_key = f"checkbox_{number}"
                        checkbox_data[standard_key] = bool(value)
                        checkbox_mapping[standard_key] = key
                        print(f"📍 [简单CHECKBOX替换] 通用映射 {key} -> {standard_key}")

        print(f"📍 [简单CHECKBOX替换] 找到 {len(checkbox_data)} 个checkbox字段")
        print(f"📍 [简单CHECKBOX替换] checkbox字段详情: {checkbox_data}")

        if not checkbox_data:
            print("📍 [简单CHECKBOX替换] 没有checkbox数据，跳过处理")
            print("📍 [简单CHECKBOX替换] 原始数据示例:")
            sample_keys = list(data.keys())[:10]
            for key in sample_keys:
                print(f"  {key}: {data[key]} (类型: {type(data[key])})")
            return

        # 创建反向映射：从序号到实际值
        checkbox_by_number = {}
        for key, value in checkbox_data.items():
            # 提取数字
            import re
            numbers = re.findall(r'\d+', key)
            if numbers:
                number = int(numbers[-1])
                checkbox_by_number[number] = value

        print(f"📍 [简单CHECKBOX替换] 按序号排序的checkbox: {dict(sorted(checkbox_by_number.items()))}")

        # 定义复选框字符（与扫描器保持一致）
        checkbox_chars = [
            '☐', '□', '■', '☑', '☒', '✓', '✔', '✗', '✘', '×', '☓',
            '▢', '▣', '⬜', '⬛', '◻', '◼', '◯', '●', '○', '◉',
            '❏', '❐', '❑', '❒', '⊞', '⊟', '⊠', '⊡',
            '🔲', '🔳'
        ]

        # 勾选和未勾选的字符
        checked_char = '☑'    # 统一使用这个字符表示勾选
        unchecked_char = '□'  # 统一使用这个字符表示未勾选

        # 全局计数器
        checkbox_counter = 0

        # 处理段落
        for paragraph in doc.paragraphs:
            paragraph_text = paragraph.text
            new_text = ""

            for char in paragraph_text:
                if char in checkbox_chars:
                    checkbox_counter += 1

                    # 使用序号映射查找checkbox状态
                    if checkbox_counter in checkbox_by_number:
                        is_checked = checkbox_by_number[checkbox_counter]
                        new_char = checked_char if is_checked else unchecked_char
                        print(f"📍 [简单CHECKBOX替换] 段落 checkbox_{checkbox_counter}: {char} -> {new_char} ({'勾选' if is_checked else '未勾选'})")
                        new_text += new_char
                    else:
                        # 保持原字符
                        new_text += char
                        print(f"📍 [简单CHECKBOX替换] 段落 checkbox_{checkbox_counter}: 未找到数据，保持原字符 {char}")
                else:
                    new_text += char

            # 更新段落文本
            if new_text != paragraph_text:
                paragraph.clear()
                paragraph.add_run(new_text)

        # 处理表格
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        paragraph_text = paragraph.text
                        new_text = ""

                        for char in paragraph_text:
                            if char in checkbox_chars:
                                checkbox_counter += 1

                                # 使用序号映射查找checkbox状态
                                if checkbox_counter in checkbox_by_number:
                                    is_checked = checkbox_by_number[checkbox_counter]
                                    new_char = checked_char if is_checked else unchecked_char
                                    print(f"📍 [简单CHECKBOX替换] 表格 checkbox_{checkbox_counter}: {char} -> {new_char} ({'勾选' if is_checked else '未勾选'})")
                                    new_text += new_char
                                else:
                                    # 保持原字符
                                    new_text += char
                                    print(f"📍 [简单CHECKBOX替换] 表格 checkbox_{checkbox_counter}: 未找到数据，保持原字符 {char}")
                            else:
                                new_text += char

                        # 更新段落文本
                        if new_text != paragraph_text:
                            paragraph.clear()
                            paragraph.add_run(new_text)

        print(f"📍 [简单CHECKBOX替换] 处理完成，共处理 {checkbox_counter} 个复选框")

    def _replace_placeholders(self, doc, data):
        """
        替换Word文档中的所有占位符

        Args:
            doc (Document): Word文档对象
            data (dict): 要替换的数据
        """
        import logging
        logger = logging.getLogger('document.placeholder.processor')

        # 处理段落中的占位符
        print("处理段落中的占位符")
        for paragraph in doc.paragraphs:
            if '{{' in paragraph.text and '}}' in paragraph.text:
                print(f"替换段落中的占位符: {paragraph.text}")
                self._replace_in_paragraph(paragraph, data)

        # 处理表格中的占位符
        print("处理表格中的占位符")
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                for cell_idx, cell in enumerate(row.cells):
                    for paragraph in cell.paragraphs:
                        if '{{' in paragraph.text and '}}' in paragraph.text:
                            print(f"替换表格[{table_idx}]行[{row_idx}]单元格[{cell_idx}]中的占位符: {paragraph.text}")
                            self._replace_in_paragraph(paragraph, data)

    def _process_checkboxes(self, doc, data):
        """
        处理Word文档中的所有复选框

        Args:
            doc (Document): Word文档对象
            data (dict): 数据字典
        """
        import logging
        logger = logging.getLogger('document.checkbox.processor')

        if not self.config.get('process_checkboxes', False):
            print("复选框处理未启用，跳过")
            return

        # 检查是否启用合并处理
        combine_checkboxes = self.config.get('combine_checkboxes', False)

        if combine_checkboxes:
            print("使用合并模式处理文档中的所有复选框")
            self._process_checkboxes_combined(doc, data)
        else:
            print("使用传统模式逐个处理复选框")
            self._process_checkboxes_individual(doc, data)

    def _process_checkboxes_combined(self, doc, data):
        """
        使用合并模式处理文档中的所有复选框

        Args:
            doc (Document): Word文档对象
            data (dict): 数据字典
        """
        try:
            # 创建配置字典
            config = {
                'use_llm_for_checkbox': self.config.get('use_llm_for_checkbox', False),
                'case_type_keywords': self.case_type_keywords,
                'column_identifiers': self.column_identifiers,
                'ocr_original_text': self.config.get('ocr_original_text', ''),
                'combine_checkboxes': True,
                'process_checkbox_batch': self.config.get('process_checkbox_batch', False)
            }

            # 添加LLM配置（使用复选框专用配置）
            if config['use_llm_for_checkbox']:
                # 优先使用复选框专用配置，如果没有则使用主配置
                llm_config = {
                    'llm_api_url': self.config.get('llm_api_url') or self.llm_api_url,
                    'llm_api_key': self.config.get('llm_api_key') or self.llm_api_key,
                    'llm_model': self.config.get('llm_model') or self.llm_model,
                    'llm_temperature': self.config.get('llm_temperature') or self.llm_temperature,
                    'llm_max_tokens': self.config.get('llm_max_tokens') or self.llm_max_tokens,
                    'llm_top_p': self.config.get('llm_top_p') or self.llm_top_p,
                    # 不再传递共享的LLM处理器，让复选框处理器使用自己的配置
                }
                config.update(llm_config)
                print(f"复选框处理器将使用LLM配置: URL={llm_config['llm_api_url']}, Model={llm_config['llm_model']}")

            # 创建复选框处理器并使用文档级别的处理
            checkbox_processor = CheckboxProcessor(data, config)
            checkbox_processor.process_document_checkboxes(doc)

        except Exception as e:
            error_msg = f"合并处理复选框时出错: {str(e)}"
            logger.error(error_msg)
            print(f"合并处理失败: {str(e)}")
            # 检查是否是LLM API相关错误，如果是则重新抛出以便上层处理
            if "LLM API调用失败" in str(e) or "timeout" in str(e).lower() or "connection" in str(e).lower():
                raise Exception(f"文档生成中LLM API调用失败: {str(e)}")
            # 其他错误则回退到传统处理模式
            print("回退到传统模式处理")
            self._process_checkboxes_individual(doc, data)

    def _process_checkboxes_individual(self, doc, data):
        """
        使用传统模式处理复选框（支持批量并发）

        Args:
            doc (Document): Word文档对象
            data (dict): 数据字典
        """
        # 检查是否启用批量处理
        process_checkbox_batch = self.config.get('process_checkbox_batch', False)

        if process_checkbox_batch:
            print("📍 [传统模式] 启用批量并发处理")
            self._process_checkboxes_individual_batch(doc, data)
        else:
            print("📍 [传统模式] 使用顺序处理")
            self._process_checkboxes_individual_sequential(doc, data)

    def _process_checkboxes_individual_batch(self, doc, data):
        """
        传统模式下的批量并发处理

        Args:
            doc (Document): Word文档对象
            data (dict): 数据字典
        """
        print("🚀 [传统模式-BATCH] 收集所有需要处理的复选框段落")

        # 收集所有包含复选框的段落和表格行
        checkbox_items = []

        # 处理所有段落中的复选框
        print("🚀 [传统模式-BATCH] 扫描普通段落中的复选框")
        for paragraph in doc.paragraphs:
            if self._has_checkboxes(paragraph):
                checkbox_items.append({
                    'type': 'paragraph',
                    'paragraph': paragraph,
                    'table_context': None
                })

        # 处理表格中的复选框
        print("🚀 [传统模式-BATCH] 扫描表格中的复选框")
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                # 获取该行第一列的文本作为上下文
                first_column_text = ""
                if len(row.cells) > 0:
                    for para in row.cells[0].paragraphs:
                        first_column_text += para.text + " "

                # 创建表格上下文字典
                table_context = {
                    "table_idx": table_idx,
                    "row_idx": row_idx,
                    "first_column_text": first_column_text.strip(),
                    "row_text": " ".join([cell.text for cell in row.cells])
                }

                # 遍历该行的所有单元格
                for cell_idx, cell in enumerate(row.cells):
                    table_context["cell_idx"] = cell_idx

                    # 遍历单元格中的所有段落
                    for para in cell.paragraphs:
                        if self._has_checkboxes(para):
                            checkbox_items.append({
                                'type': 'table_cell',
                                'paragraph': para,
                                'table_context': table_context.copy()
                            })

        print(f"🚀 [传统模式-BATCH] 找到 {len(checkbox_items)} 个包含复选框的段落")

        if checkbox_items:
            # 使用批量处理
            self._handle_checkboxes_batch(checkbox_items, data, doc)
        else:
            print("🚀 [传统模式-BATCH] 没有找到需要处理的复选框")

    def _process_checkboxes_individual_sequential(self, doc, data):
        """
        传统模式下的顺序处理（原有逻辑）

        Args:
            doc (Document): Word文档对象
            data (dict): 数据字典
        """
        # 处理所有段落中的复选框
        print("⏳ [传统模式-顺序] 处理所有段落中的复选框")
        for paragraph in doc.paragraphs:
            self._handle_checkboxes(paragraph, data)

        # 处理表格中的复选框
        print("⏳ [传统模式-顺序] 处理表格中的复选框")
        for table_idx, table in enumerate(doc.tables):
            for row_idx, row in enumerate(table.rows):
                # 获取该行第一列的文本作为上下文
                first_column_text = ""
                if len(row.cells) > 0:
                    for para in row.cells[0].paragraphs:
                        first_column_text += para.text + " "

                # 创建表格上下文字典
                table_context = {
                    "table_idx": table_idx,
                    "row_idx": row_idx,
                    "first_column_text": first_column_text.strip(),
                    "row_text": " ".join([cell.text for cell in row.cells])
                }

                # 遍历该行的所有单元格
                for cell_idx, cell in enumerate(row.cells):
                    table_context["cell_idx"] = cell_idx

                    # 遍历单元格中的所有段落
                    for para in cell.paragraphs:
                        self._handle_checkboxes(para, data, table_context)

    def _has_checkboxes(self, paragraph):
        """
        检查段落是否包含复选框字符

        Args:
            paragraph: 段落对象

        Returns:
            bool: 是否包含复选框
        """
        checkbox_chars = ['☐', '□', '■', '☑', '☒', '✓', '✔']
        return any(char in paragraph.text for char in checkbox_chars)

    def _handle_checkboxes_batch(self, checkbox_items, data, doc):
        """
        批量处理复选框项目

        Args:
            checkbox_items: 包含复选框的项目列表
            data: 案件数据
            doc: Word文档对象
        """
        print(f"🚀 [传统模式-BATCH] 开始批量处理 {len(checkbox_items)} 个复选框项目")

        # 创建配置字典
        config = {
            'use_llm_for_checkbox': self.config.get('use_llm_for_checkbox', False),
            'case_type_keywords': self.case_type_keywords,
            'column_identifiers': self.column_identifiers,
            'ocr_original_text': self.config.get('ocr_original_text', ''),
            'combine_checkboxes': self.config.get('combine_checkboxes', False),
            'combine_checkboxes_by': self.config.get('combine_checkboxes_by', 'fulltext'),
            'process_checkbox_batch': self.config.get('process_checkbox_batch', False)
        }

        # 添加LLM配置
        if config['use_llm_for_checkbox']:
            llm_config = {
                'llm_api_url': self.llm_api_url,
                'llm_api_key': self.llm_api_key,
                'llm_model': self.llm_model,
                'llm_temperature': self.llm_temperature,
                'llm_max_tokens': self.llm_max_tokens,
                'llm_top_p': self.llm_top_p
            }

            # 添加共享LLM处理器
            if self.shared_llm_processor:
                llm_config['shared_llm_processor'] = self.shared_llm_processor

            config.update(llm_config)

        try:
            # 创建复选框处理器
            checkbox_processor = CheckboxProcessor(data, config)

            # 构建表格行数据（模拟合并模式的数据结构）
            table_rows_with_checkboxes = {}

            for i, item in enumerate(checkbox_items):
                paragraph = item['paragraph']
                table_context = item['table_context']

                # 为每个项目创建一个唯一的行ID
                if table_context:
                    row_id = f"table_{table_context['table_idx']}_row_{table_context['row_idx']}_cell_{table_context['cell_idx']}"
                    first_column = table_context.get('first_column_text', '')
                    row_text = paragraph.text
                    full_text = f"{first_column} {row_text}".strip()
                else:
                    row_id = f"paragraph_{i}"
                    first_column = ""
                    row_text = paragraph.text
                    full_text = row_text

                # 计算复选框数量
                checkbox_chars = ['☐', '□', '■', '☑', '☒', '✓', '✔']
                checkbox_count = sum(paragraph.text.count(char) for char in checkbox_chars)

                if checkbox_count > 0:
                    table_rows_with_checkboxes[row_id] = {
                        'table_idx': table_context['table_idx'] if table_context else 0,
                        'row_idx': table_context['row_idx'] if table_context else i,
                        'first_column': first_column,
                        'row_text': row_text,
                        'full_text': full_text,
                        'checkbox_count': checkbox_count,
                        'paragraph': paragraph,  # 保存段落引用以便后续处理
                        'table_context': table_context
                    }

            print(f"🚀 [传统模式-BATCH] 构建了 {len(table_rows_with_checkboxes)} 个表格行数据")

            if table_rows_with_checkboxes:
                # 直接调用文档级处理，使用真实文档
                print("🚀 [传统模式-BATCH] 调用文档级批量处理（使用真实文档）")

                # 设置处理器的table_row_runs
                checkbox_processor.table_row_runs = {}
                for row_id, row_info in table_rows_with_checkboxes.items():
                    checkbox_processor.table_row_runs[row_id] = {
                        'table_idx': row_info['table_idx'],
                        'row_idx': row_info['row_idx'],
                        'first_column': row_info['first_column'],
                        'row_text': row_info['row_text'],
                        'full_text': row_info['full_text'],
                        'runs': []
                    }

                # 直接调用批量处理，使用真实文档
                print("🚀 [传统模式-BATCH] 调用 process_document_checkboxes 处理真实文档")
                checkbox_processor.process_document_checkboxes(doc)
            else:
                print("🚀 [传统模式-BATCH] 没有有效的复选框数据需要处理")

        except Exception as e:
            print(f"🚀 [传统模式-BATCH] 批量处理失败: {str(e)}")
            # 回退到顺序处理
            print("🚀 [传统模式-BATCH] 回退到顺序处理")
            for item in checkbox_items:
                self._handle_checkboxes(item['paragraph'], data, item['table_context'])

    def _apply_batch_results_to_checkboxes(self, checkbox_items, table_rows_with_checkboxes, batch_results):
        """
        将批量处理的结果应用到实际的复选框

        Args:
            checkbox_items: 原始的复选框项目列表
            table_rows_with_checkboxes: 表格行数据（包含LLM结果）
            batch_results: 批量处理的结果字典
        """
        print("🚀 [传统模式-BATCH] 开始将LLM结果应用到实际复选框")

        # 创建一个映射，从row_id到checkbox_items
        row_id_to_items = {}
        for item in checkbox_items:
            paragraph = item['paragraph']
            table_context = item['table_context']

            if table_context:
                row_id = f"table_{table_context['table_idx']}_row_{table_context['row_idx']}_cell_{table_context['cell_idx']}"
            else:
                # 为普通段落创建一个唯一ID
                row_id = f"paragraph_{id(paragraph)}"

            if row_id not in row_id_to_items:
                row_id_to_items[row_id] = []
            row_id_to_items[row_id].append(item)

        # 遍历表格行数据，获取LLM结果并应用到复选框
        for row_id, row_info in table_rows_with_checkboxes.items():
            if row_id in row_id_to_items:
                items = row_id_to_items[row_id]

                # 从CheckboxProcessor的table_row_runs中获取结果
                # 注意：这里需要一种方式来获取LLM的结果
                # 由于我们没有直接的方式获取结果，我们需要重新处理这些复选框

                print(f"🚀 [传统模式-BATCH] 处理行 {row_id} 的 {len(items)} 个复选框项目")

                # 获取该行的复选框
                all_checkboxes = []
                for item in items:
                    paragraph = item['paragraph']
                    checkboxes = self._get_checkboxes_in_paragraph_simple(paragraph)
                    all_checkboxes.extend(checkboxes)

                # 从batch_results获取LLM结果
                llm_results = batch_results.get(row_id, [False] * len(all_checkboxes))

                print(f"🚀 [传统模式-BATCH] 行 {row_id} 的LLM结果: {llm_results}")

                # 应用结果到复选框
                for i, (checkbox, should_check) in enumerate(zip(all_checkboxes, llm_results)):
                    self._set_checkbox_state_simple(checkbox, should_check)
                    print(f"🚀 [传统模式-BATCH] 复选框 #{i+1} 设置为: {'勾选' if should_check else '不勾选'}")
            else:
                print(f"🚀 [传统模式-BATCH] 警告: 未找到行 {row_id} 对应的复选框项目")

        print("🚀 [传统模式-BATCH] 复选框状态应用完成")

    def _get_checkboxes_in_paragraph_simple(self, paragraph):
        """
        简单获取段落中的复选框（用于传统模式批量处理）
        """
        checkboxes = []
        paragraph_text = paragraph.text
        checkbox_chars = ['☐', '□', '■', '☑', '☒', '✓', '✔']

        for i, char in enumerate(paragraph_text):
            if char in checkbox_chars:
                checkboxes.append({
                    'paragraph': paragraph,
                    'position': i,
                    'checkbox_char': char,
                    'original_text': paragraph_text
                })

        return checkboxes

    def _get_llm_results_for_row(self, row_id, checkbox_count):
        """
        获取指定行的LLM结果

        Args:
            row_id: 行ID
            checkbox_count: 复选框数量

        Returns:
            布尔值列表
        """
        # 这里应该从某个地方获取真实的LLM结果
        # 暂时返回全部False作为占位符
        # 在实际实现中，需要从CheckboxProcessor或其他地方获取真实结果
        return [False] * checkbox_count

    def _set_checkbox_state_simple(self, checkbox, should_check):
        """
        简单设置复选框状态（用于传统模式批量处理）
        """
        try:
            paragraph = checkbox['paragraph']
            position = checkbox['position']

            # 获取段落文本
            paragraph_text = paragraph.text

            # 创建新文本
            new_text_chars = list(paragraph_text)
            new_text_chars[position] = "☑" if should_check else "□"
            new_text = "".join(new_text_chars)

            # 更新段落文本
            # 清空所有run并添加新的run
            while len(paragraph.runs) > 0:
                paragraph._p.remove(paragraph.runs[0]._element)

            paragraph.add_run(new_text)

            print(f"🚀 [传统模式-BATCH] 复选框位置 {position} 已更新: '{checkbox['checkbox_char']}' -> '{'☑' if should_check else '□'}'")

        except Exception as e:
            print(f"🚀 [传统模式-BATCH] 设置复选框状态失败: {str(e)}")

    def _handle_checkboxes(self, paragraph, data, table_context=None):
        """
        处理段落中的复选框（打钩）

        Args:
            paragraph (Paragraph): 段落对象
            data (dict): 数据字典
            table_context (dict, optional): 表格上下文信息，包含行索引、单元格索引、第一列文本等
        """
        import logging
        logger = logging.getLogger('document.checkbox.processor')

        # 检查是否需要处理复选框
        if not self.config.get('process_checkboxes', False):
            print("复选框处理已被关闭")
            return

        # 创建配置字典
        config = {
            'use_llm_for_checkbox': self.config.get('use_llm_for_checkbox', False),
            'case_type_keywords': self.case_type_keywords,
            'column_identifiers': self.column_identifiers,
            'ocr_original_text': self.config.get('ocr_original_text', ''),  # 添加OCR原始文本
            'combine_checkboxes': self.config.get('combine_checkboxes', False),  # 添加复选框合并配置
            'combine_checkboxes_by': self.config.get('combine_checkboxes_by', 'fulltext'),  # 添加复选框合并策略配置
            'process_checkbox_batch': self.config.get('process_checkbox_batch', False)  # 添加批量处理配置
        }

        # 仅当明确指定使用LLM时，才添加LLM相关配置
        if config['use_llm_for_checkbox']:
            # 先获取默认的LLM参数
            llm_config = {
                'llm_api_url': self.llm_api_url,
                'llm_api_key': self.llm_api_key,
                'llm_model': self.llm_model,
                'llm_temperature': self.llm_temperature,
                'llm_max_tokens': self.llm_max_tokens,
                'llm_top_p': self.llm_top_p
            }

            # 检查是否有复选框专用的LLM参数
            checkbox_api_url = os.environ.get('CHECKBOX_LLM_API_URL')
            checkbox_api_key = os.environ.get('CHECKBOX_LLM_API_KEY')
            checkbox_temp = os.environ.get('CHECKBOX_LLM_TEMPERATURE')
            checkbox_max_tokens = os.environ.get('CHECKBOX_LLM_MAX_TOKENS')
            checkbox_model = os.environ.get('CHECKBOX_LLM_MODEL')
            checkbox_top_p = os.environ.get('CHECKBOX_LLM_TOP_P')  # 获取复选框专用的top_p参数

            # 如果环境变量中有定义，则使用环境变量的值
            if checkbox_api_url:
                llm_config['llm_api_url'] = checkbox_api_url

            if checkbox_api_key:
                llm_config['llm_api_key'] = checkbox_api_key

            if checkbox_temp:
                try:
                    llm_config['llm_temperature'] = float(checkbox_temp)
                except (ValueError, TypeError):
                    logger.warning(f"复选框LLM温度参数格式不正确: {checkbox_temp}")

            if checkbox_max_tokens:
                try:
                    llm_config['llm_max_tokens'] = int(checkbox_max_tokens)
                except (ValueError, TypeError):
                    logger.warning(f"复选框LLM最大token参数格式不正确: {checkbox_max_tokens}")

            if checkbox_model:
                llm_config['llm_model'] = checkbox_model

            # 处理top_p参数
            if checkbox_top_p:
                try:
                    llm_config['llm_top_p'] = float(checkbox_top_p)
                except (ValueError, TypeError):
                    logger.warning(f"复选框LLM top_p参数格式不正确: {checkbox_top_p}")

            # 添加共享LLM处理器到配置中
            if self.shared_llm_processor:
                llm_config['shared_llm_processor'] = self.shared_llm_processor

            # 更新配置
            config.update(llm_config)
            # 只在首次处理时输出信息，并且只在调试模式下输出
            if not self._checkbox_llm_info_printed and self.config.get('debug_mode', False):
                print(f"使用LLM处理复选框 (模型: {llm_config['llm_model']}, 温度: {llm_config['llm_temperature']}, top_p: {llm_config['llm_top_p']})")
                self._checkbox_llm_info_printed = True
            else:
                if not hasattr(self, '_checkbox_rule_info_printed') and self.config.get('debug_mode', False):
                    print("使用简化规则处理复选框")
                    self._checkbox_rule_info_printed = True

        try:
            # 创建复选框处理器
            checkbox_processor = CheckboxProcessor(data, config)

            # 处理段落中的复选框
            checkbox_processor.process_checkboxes(paragraph, table_context)
        except Exception as e:
            logger.error(f"处理复选框时出错: {str(e)}")
            # 降级为基本处理
            self._basic_checkbox_processing(paragraph, data, table_context)

    def _basic_checkbox_processing(self, paragraph, data, table_context=None):
        """
        基本的复选框处理逻辑，不依赖LLM

        Args:
            paragraph (Paragraph): 段落对象
            data (dict): 数据字典
            table_context (dict, optional): 表格上下文信息
        """
        import logging
        logger = logging.getLogger('document.checkbox.processor')

        # 记录处理信息
        print(f"检查段落中的复选框: '{paragraph.text}'")

        # 检查段落中是否包含复选框字符
        checkbox_chars = ['☐', '□', '■', '☑', '☒', '✓', '✔']

        # 如果段落文本中包含复选框字符，记录调试信息
        if any(char in paragraph.text for char in checkbox_chars):
            print(f"段落中包含复选框字符: '{paragraph.text}'")

        # 基本处理逻辑 - 这里只做记录，不实际处理
        # 在实际应用中，可以根据需要扩展此方法来处理复选框

    def _replace_in_paragraph(self, paragraph, data):
        """
        在段落中替换占位符

        Args:
            paragraph (Paragraph): 段落对象
            data (dict): 要替换的数据
        """
        if '{{' in paragraph.text and '}}' in paragraph.text:
            # 保存原始文本用于比较
            original_text = paragraph.text
            text = original_text

            # 处理所有字段
            for key, value in data.items():
                if isinstance(value, (str, int, float)):
                    placeholder = '{{' + key + '}}'
                    if placeholder in text:
                        print(f"替换占位符: {placeholder} -> {value}")
                        text = text.replace(placeholder, str(value))

            # 处理被告.xxx格式的占位符
            placeholders = re.findall(r'{{(.*?)}}', text)
            for placeholder in placeholders:
                placeholder_pattern = '{{' + placeholder + '}}'
                if placeholder_pattern in text:  # 如果占位符仍存在（未被替换）
                    placeholder_clean = placeholder.strip()
                    if '.' in placeholder_clean:
                        parts = placeholder_clean.split('.')
                        if len(parts) == 2 and parts[0] == '被告':
                            field_name = parts[1]
                            # 尝试在data中查找被告+字段名
                            for key in data:
                                if key.endswith(field_name) and ('被告' in key or '被告（自然人）' in key):
                                    value = data[key]
                                    if isinstance(value, (str, int, float)):
                                        print(f"替换被告占位符: {placeholder} -> {value}")
                                        text = text.replace(placeholder_pattern, str(value))
                                        break
                            else:
                                # 如果是被告列表中的第一个被告
                                if '被告（自然人）列表' in data and isinstance(data['被告（自然人）列表'], list) and data['被告（自然人）列表']:
                                    first_defendant = data['被告（自然人）列表'][0]
                                    if field_name in first_defendant:
                                        value = first_defendant[field_name]
                                        if isinstance(value, (str, int, float)):
                                            print(f"替换被告列表中的占位符: {placeholder} -> {value}")
                                            text = text.replace(placeholder_pattern, str(value))
                                        else:
                                            print(f"未找到被告占位符值，替换为空: {placeholder}")
                                            text = text.replace(placeholder_pattern, "")
                                    else:
                                        print(f"未找到被告字段，替换为空: {placeholder}")
                                        text = text.replace(placeholder_pattern, "")
                                else:
                                    print(f"未找到被告信息，替换为空: {placeholder}")
                                    text = text.replace(placeholder_pattern, "")
                    else:
                        # 对于普通占位符，如果还未被处理（找不到对应值），替换为空字符串
                        print(f"未找到普通占位符值，替换为空: {placeholder}")
                        text = text.replace(placeholder_pattern, "")

            # 更新段落文本，保留格式
            if text != original_text:
                # 收集所有段落的runs和它们的格式信息
                runs_info = []
                start_pos = 0
                for run in paragraph.runs:
                    length = len(run.text)
                    runs_info.append({
                        'text': run.text,
                        'start': start_pos,
                        'end': start_pos + length,
                        'run': run,
                        'bold': run.bold,
                        'italic': run.italic,
                        'underline': run.underline,
                        'font': run.font.name if run.font.name else None,
                        'size': run.font.size if run.font.size else None,
                    })
                    start_pos += length

                # 清除段落中的所有运行
                for run in paragraph.runs:
                    run.text = ""

                # 添加新的带格式的运行
                new_run = paragraph.add_run(text)

                # 如果有格式信息，应用到新运行
                if runs_info:
                    # 使用第一个运行的格式作为基础
                    base_format = runs_info[0]
                    if base_format['bold'] is not None:
                        new_run.bold = base_format['bold']
                    if base_format['italic'] is not None:
                        new_run.italic = base_format['italic']
                    if base_format['underline'] is not None:
                        new_run.underline = base_format['underline']
                    if base_format['font'] is not None:
                        new_run.font.name = base_format['font']
                    if base_format['size'] is not None:
                        new_run.font.size = base_format['size']

    def update_placeholders(self):
        """
        分析模板中的占位符
        """
        import re

        if not self.template_path or not os.path.exists(self.template_path):
            print(f"警告: 模板路径不存在或为空: {self.template_path}")
            return

        doc = Document(self.template_path)
        template_name = os.path.basename(self.template_path)
        placeholders = set()

        print(f"\n分析模板: {template_name}")
        print(f"模板完整路径: {self.template_path}")

        # 检查段落中的占位符
        for para in doc.paragraphs:
            matches = re.findall(r'{{(.*?)}}', para.text)
            if matches:
                placeholders.update(matches)
                print(f"找到段落占位符: {matches}")
            elif '{{' in para.text or '}}' in para.text:
                print(f"警告: 段落包含不完整占位符标记: {para.text}")

        # 检查表格中的占位符
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        matches = re.findall(r'{{(.*?)}}', para.text)
                        if matches:
                            placeholders.update(matches)
                            print(f"找到表格占位符: {matches}")
                        elif '{{' in para.text or '}}' in para.text:
                            print(f"警告: 表格单元格包含不完整占位符标记: {para.text}")

        # 清理占位符并更新字典
        clean_placeholders = [p.strip() for p in placeholders if p.strip()]
        self.placeholders[template_name] = sorted(clean_placeholders)

        if clean_placeholders:
            print(f"\n模板 {template_name} 中找到 {len(clean_placeholders)} 个占位符")
        else:
            print(f"\n警告: 模板 {template_name} 中未找到占位符")

    def generate_document(self, case_data, output_path=None):
        """
        根据案件数据生成文档

        Args:
            case_data (dict): 案件数据
            output_path (str, optional): 输出文件路径或目录路径。
                                        如果是目录路径，将在该目录下生成文件。
                                        如果为None，则使用临时文件。

        Returns:
            str: 生成的文档路径
        """
        if not self.template_path or not os.path.exists(self.template_path):
            raise ValueError(f"模板文件不存在: {self.template_path}")

        # 创建文档对象
        doc = Document(self.template_path)

        # 调用文档处理方法，分别处理占位符和复选框
        self._process_document(doc, case_data)

        # 确定输出路径
        if not output_path:
            # 创建临时文件
            import tempfile
            output_dir = tempfile.mkdtemp()
            template_name = os.path.basename(self.template_path)
            output_path = os.path.join(output_dir, f"生成_{template_name}")
        else:
            # 检查output_path是否是目录
            if os.path.isdir(output_path):
                # 如果是目录，在该目录下生成文件
                template_name = os.path.basename(self.template_path)
                # 生成唯一的文件名，避免覆盖
                timestamp = int(time.time())
                output_path = os.path.join(output_path, f"生成_{timestamp}_{template_name}")
                print(f"将在目录 {os.path.dirname(output_path)} 下生成文件: {os.path.basename(output_path)}")

        # 保存文档
        try:
            doc.save(output_path)
            print(f"文档已生成: {output_path}")
        except Exception as e:
            print(f"保存文档时出错: {str(e)}")
            raise

        return output_path
